This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2026/dev/Arch Linux) (preloaded format=pdflatex 2025.7.9)  11 JUL 2025 20:35
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**PuntoK_Trigonometria_v1.tex
(./PuntoK_Trigonometria_v1.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/usr/share/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(/usr/share/texmf-dist/tex/latex/a4wide/a4wide.sty
Package: a4wide 1994/08/30

(/usr/share/texmf-dist/tex/latex/ntgclass/a4.sty
Package: a4 2023/01/10 v1.2g A4 based page layout
))
(/usr/share/texmf-dist/tex/latex/graphics/color.sty
Package: color 2024/06/23 v1.3e Standard LaTeX Color (DPC)

(/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx))
(/usr/share/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks19
\verbatim@line=\toks20
\verbatim@in@stream=\read2
)
(/usr/share/texmf/tex/latex/Sweave.sty
Package: Sweave 

(/usr/share/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
(/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
(/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks21
)
(/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
)
(/usr/share/texmf-dist/tex/latex/fancyvrb/fancyvrb.sty
Package: fancyvrb 2024/01/20 4.5c verbatim text (tvz,hv)
\FV@CodeLineNo=\count270
\FV@InFile=\read3
\FV@TabBox=\box52
\c@FancyVerbLine=\count271
\FV@StepNumber=\count272
\FV@OutFile=\write3
)
(/usr/share/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
))
(/usr/share/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(/usr/share/texmf-dist/tex/latex/xargs/xargs.sty
Package: xargs 2008/03/22 v1.1  extended macro definitions  (mpg)

(/usr/share/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(/usr/share/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(/usr/share/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks22
\XKV@tempa@toks=\toks23
)
\XKV@depth=\count273
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\xargs@max=\count274
\xargs@toksa=\toks24
\xargs@toksb=\toks25
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks26
\ex@=\dimen144
))
(/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count275
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count276
\leftroot@=\count277
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count278
\DOTSCASE@=\count279
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count280
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count281
\dotsspace@=\muskip18
\c@parentequation=\count282
\dspbrk@lvl=\count283
\tag@help=\toks27
\row@=\count284
\column@=\count285
\maxfields@=\count286
\andhelp@=\toks28
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks29
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks30
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen153
\lightrulewidth=\dimen154
\cmidrulewidth=\dimen155
\belowrulesep=\dimen156
\belowbottomsep=\dimen157
\aboverulesep=\dimen158
\abovetopsep=\dimen159
\cmidrulesep=\dimen160
\cmidrulekern=\dimen161
\defaultaddspace=\dimen162
\@cmidla=\count287
\@cmidlb=\count288
\@aboverulesep=\dimen163
\@belowrulesep=\dimen164
\@thisruleclass=\count289
\@lastruleclass=\count290
\@thisrulewidth=\dimen165
)
(/usr/share/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2024-10-27 v4.22 Multi-page Table package (DPC)
\LTleft=\skip54
\LTright=\skip55
\LTpre=\skip56
\LTpost=\skip57
\LTchunksize=\count291
\LTcapwidth=\dimen166
\LT@head=\box55
\LT@firsthead=\box56
\LT@foot=\box57
\LT@lastfoot=\box58
\LT@gbox=\box59
\LT@cols=\count292
\LT@rows=\count293
\c@LT@tables=\count294
\c@LT@chunks=\count295
\LT@p@ftn=\toks31
)
(/usr/share/texmf-dist/tex/latex/eurosym/eurosym.sty
Package: eurosym 1998/08/06 v1.1 European currency symbol ``Euro''
\@eurobox=\box60
)
(/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks32
\pgfutil@tempdima=\dimen167
\pgfutil@tempdimb=\dimen168
)
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box61
)
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks33
\pgfkeys@temptoks=\toks34

(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.te
x
\pgfkeys@tmptoks=\toks35
))
\pgf@x=\dimen169
\pgf@y=\dimen170
\pgf@xa=\dimen171
\pgf@ya=\dimen172
\pgf@xb=\dimen173
\pgf@yb=\dimen174
\pgf@xc=\dimen175
\pgf@yc=\dimen176
\pgf@xd=\dimen177
\pgf@yd=\dimen178
\w@pgf@writea=\write4
\r@pgf@reada=\read4
\c@pgf@counta=\count296
\c@pgf@countb=\count297
\c@pgf@countc=\count298
\c@pgf@countd=\count299
\t@pgf@toka=\toks36
\t@pgf@tokb=\toks37
\t@pgf@tokc=\toks38
\pgf@sys@id@count=\count300
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count301
\pgfsyssoftpath@bigbuffer@items=\count302
)
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
LaTeX Info: Redefining \color on input line 762.

(/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen179
\pgfmath@count=\count303
\pgfmath@box=\box62
\pgfmath@toks=\toks39
\pgfmath@stack@operand=\toks40
\pgfmath@stack@operation=\toks41
)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code
.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.te
x) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics
.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count304
))
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen180
\pgf@picmaxx=\dimen181
\pgf@picminy=\dimen182
\pgf@picmaxy=\dimen183
\pgf@pathminx=\dimen184
\pgf@pathmaxx=\dimen185
\pgf@pathminy=\dimen186
\pgf@pathmaxy=\dimen187
\pgf@xx=\dimen188
\pgf@xy=\dimen189
\pgf@yx=\dimen190
\pgf@yy=\dimen191
\pgf@zx=\dimen192
\pgf@zy=\dimen193
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen194
\pgf@path@lasty=\dimen195
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen196
\pgf@shorten@start@additional=\dimen197
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box63
\pgf@hbox=\box64
\pgf@layerbox@main=\box65
\pgf@picture@serial@count=\count305
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen198
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.t
ex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen199
\pgf@pt@y=\dimen256
\pgf@pt@temp=\dimen257
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.te
x
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen258
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen259
\pgf@sys@shading@range@num=\count306
\pgf@shadingcount=\count307
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box66
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box67
)
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen260
\pgf@nodesepend=\dimen261
)
(/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen262
\pgffor@skip=\dimen263
\pgffor@stack=\toks42
\pgffor@toks=\toks43
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.te
x
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count308
\pgfplotmarksize=\dimen264
)
\tikz@lastx=\dimen265
\tikz@lasty=\dimen266
\tikz@lastxsaved=\dimen267
\tikz@lastysaved=\dimen268
\tikz@lastmovetox=\dimen269
\tikz@lastmovetoy=\dimen270
\tikzleveldistance=\dimen271
\tikzsiblingdistance=\dimen272
\tikz@figbox=\box68
\tikz@figbox@bg=\box69
\tikz@tempbox=\box70
\tikz@tempbox@bg=\box71
\tikztreelevel=\count309
\tikznumberofchildren=\count310
\tikznumberofcurrentchild=\count311
\tikz@fig@count=\count312
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count313
\pgfmatrixcurrentcolumn=\count314
\pgf@matrix@numberofcolumns=\count315
)
\tikz@expandcount=\count316

(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
topaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks44
\t@pgfplots@tokb=\toks45
\t@pgfplots@tokc=\toks46
\pgfplots@tmpa=\dimen273
\c@pgfplots@coordindex=\count317
\c@pgfplots@scanlineindex=\count318

(/usr/share/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_l
oader.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks47
\t@pgf@tokb=\toks48
\t@pgf@tokc=\toks49

(/usr/share/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_p
gfutil-common-lists.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure
.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure
ext.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.code.te
x
\c@pgfplotsarray@tmp=\count319
)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.code.t
ex)
(/usr/share/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.code.t
ex
\c@pgfplotstable@counta=\count320
\t@pgfplotstable@a=\toks50
)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.code.te
x) (/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfshading
.code.tex
\c@pgfplotslibrarysurf@no=\count321

(/usr/share/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfshading.
pgfsys-pdftex.def)))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex)))
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.tex
\pgfdecoratedcompleteddistance=\dimen274
\pgfdecoratedremainingdistance=\dimen275
\pgfdecoratedinputsegmentcompleteddistance=\dimen276
\pgfdecoratedinputsegmentremainingdistance=\dimen277
\pgf@decorate@distancetomove=\dimen278
\pgf@decorate@repeatstate=\count322
\pgfdecorationsegmentamplitude=\dimen279
\pgfdecorationsegmentlength=\dimen280
)
\tikz@lib@dec@box=\box72
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.pathmorphing.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.pathmorphing.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.pathreplacing.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.pathreplacing.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.contourlua
.code.tex)
\pgfplots@numplots=\count323
\pgfplots@xmin@reg=\dimen281
\pgfplots@xmax@reg=\dimen282
\pgfplots@ymin@reg=\dimen283
\pgfplots@ymax@reg=\dimen284
\pgfplots@zmin@reg=\dimen285
\pgfplots@zmax@reg=\dimen286
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
plotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
arrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen287
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
calc.code.tex
File: tikzlibrarycalc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
positioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count324
\l__pdf_internal_box=\box73
)
(./PuntoK_Trigonometria_v1.aux)
\openout1 = `PuntoK_Trigonometria_v1.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.

(/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count325
\scratchdimen=\dimen288
\scratchbox=\box74
\nofMPsegments=\count326
\nofMParguments=\count327
\everyMPshowfont=\toks51
\MPscratchCnt=\count328
\MPscratchDim=\dimen289
\MPnumerator=\count329
\makeMPintoPDFobject=\count330
\everyMPtoPDFconversion=\toks52
) (/usr/share/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Package pgfplots notification 'compat/show suggested version=true': document ha
s been generated with the most recent feature set (\pgfplotsset{compat=1.18}).

(./PuntoK_Trigonometria_v1-concordance.tex)
<diagrama.png, id=5, 652.4375pt x 463.7325pt>
File: diagrama.png Graphic file (type png)
<use diagrama.png>
Package pdftex.def Info: diagrama.png  used on input line 30.
(pdftex.def)             Requested size: 227.62204pt x 161.78517pt.

Overfull \hbox (52.23108pt too wide) in paragraph at lines 30--32
[][] \T1/cmr/m/n/10 <U+00BF>Cu<U+00E1>l es la gr<U+00E1>fica
 []

<grafica_A.png, id=7, 652.4375pt x 436.63126pt>
File: grafica_A.png Graphic file (type png)
<use grafica_A.png>
Package pdftex.def Info: grafica_A.png  used on input line 36.
(pdftex.def)             Requested size: 113.81102pt x 76.1651pt.
<grafica_B.png, id=8, 652.4375pt x 436.63126pt>
File: grafica_B.png Graphic file (type png)
<use grafica_B.png>
Package pdftex.def Info: grafica_B.png  used on input line 39.
(pdftex.def)             Requested size: 113.81102pt x 76.1651pt.
<grafica_C.png, id=9, 652.4375pt x 436.63126pt>
File: grafica_C.png Graphic file (type png)
<use grafica_C.png>
Package pdftex.def Info: grafica_C.png  used on input line 43.
(pdftex.def)             Requested size: 113.81102pt x 76.1651pt.
<grafica_D.png, id=10, 652.4375pt x 436.63126pt>
File: grafica_D.png Graphic file (type png)
<use grafica_D.png>
Package pdftex.def Info: grafica_D.png  used on input line 46.
(pdftex.def)             Requested size: 113.81102pt x 76.1651pt.
! Undefined control sequence.
l.101 \exname
             {Trigonometr<U+00ED>a - Interpretaci<U+00F3>n de Gr<U+00E1>ficas}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.102 \extype
             {schoice}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.103 \exsolution
                 {0001}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.104 \exshuffle
                {TRUE}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.105 \exsection
                {Geometr<U+00ED>a - Trigonometr<U+00ED>a}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.



[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texmf-dist/fonts
/enc/dvips/cm-super/cm-super-t1.enc} <./diagrama.png> <./grafica_A.png> <./graf
ica_B.png> <./grafica_C.png> <./grafica_D.png>]
(./PuntoK_Trigonometria_v1.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 24666 strings out of 475109
 633403 string characters out of 5765326
 1144008 words of memory out of 5000000
 47266 multiletter control sequences out of 15000+600000
 560054 words of font info for 39 fonts, out of 8000000 for 9000
 40 hyphenation exceptions out of 8191
 84i,10n,91p,744b,609s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/
texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb></usr/share/texmf-dist/font
s/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texmf-dist/fonts/type1/public/
amsfonts/cm/cmr7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy
10.pfb></usr/share/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb>
Output written on PuntoK_Trigonometria_v1.pdf (1 page, 180749 bytes).
PDF statistics:
 54 PDF objects out of 1000 (max. 8388607)
 27 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 42 words of extra memory for PDF output out of 10000 (max. 10000000)

