\documentclass[10pt,a4paper]{article}

%% paquetes
\usepackage[utf8]{inputenc}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable,eurosym}
\usepackage{tikz}
\usepackage{pgfplots}
\pgfplotsset{compat=1.18}
\usetikzlibrary{arrows,calc,positioning}

%% nuevos entornos
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\SweaveOpts{concordance=TRUE}

\begin{enumerate}

<<echo=FALSE, results=hide>>=
# Configuración inicial
Sys.setlocale("LC_ALL", "C")
options(OutDec = ".")

# Cargar librerías esenciales
library(exams)
library(digest)
library(testthat)
library(knitr)

# Configurar knitr para TikZ
knitr::opts_chunk$set(
  echo = FALSE,
  results = "hide",
  fig.path = "figure/",
  fig.align = "center"
)

# Configuración TikZ
options(tikzLatex = "pdflatex")

# Establecer semilla aleatoria
set.seed(sample(1:100000, 1))

# Determinar tipo de gráfico para exams
typ <- switch(match_exams_call(),
  "exams2nops" = "tex",
  "exams2html" = "svg",
  "exams2pdf" = "pdf",
  match_exams_device()
)

# Función para generar datos aleatorios con 300+ versiones únicas
generar_datos <- function() {
  # Parámetros geométricos aleatorios (ampliado para más diversidad)
  h_valor <- sample(seq(1.5, 10, 0.25), 1)  # altura h (más opciones)
  angulo_inicial <- sample(seq(10, 50, 2), 1)  # ángulo inicial en grados

  # Contextos aleatorios expandidos
  contextos <- c(
    "Un punto K se mueve sobre un segmento QT",
    "Un objeto K se desplaza a lo largo de una recta QT",
    "Una partícula K recorre el segmento QT",
    "Un móvil K se traslada sobre la línea QT",
    "Un elemento K se desliza por el segmento QT",
    "Una masa K se mueve a través del segmento QT",
    "Un cuerpo K navega sobre la línea QT",
    "Una pieza K se transporta por el segmento QT",
    "Un componente K circula sobre la recta QT",
    "Una unidad K se desplaza en el segmento QT"
  )

  contexto <- sample(contextos, 1)

  # Colores aleatorios para las gráficas (expandido)
  colores <- c("blue", "red", "green", "orange", "purple", "brown",
               "cyan", "magenta", "gray", "black", "pink", "yellow")
  color_grafica <- sample(colores, 1)

  # Variables adicionales para diversidad
  nombre_punto_movil <- sample(c("K", "M", "N", "R", "S"), 1)
  nombre_segmento_inicio <- sample(c("Q", "A", "X", "P", "L"), 1)
  nombre_segmento_fin <- sample(c("T", "B", "Y", "R", "M"), 1)

  # Parámetros de escala para gráficas
  escala_x <- sample(seq(0.6, 1.4, 0.1), 1)
  escala_y <- sample(seq(0.7, 1.3, 0.1), 1)

  # Generar opciones de gráficas (4 tipos diferentes)
  tipos_graficas <- c("constante", "decreciente", "creciente_decreciente", "decreciente_creciente")

  # Respuesta correcta: función decreciente (sen α = h/KP, entonces KP = h/sen α)
  # Cuando α aumenta de 0 a 90°, sen α aumenta, por lo tanto KP disminuye
  respuesta_correcta <- "decreciente"

  # Sistema avanzado de distractores con valores duplicados (30% probabilidad)
  permitir_valores_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))

  # Generar distractores
  distractores <- tipos_graficas[tipos_graficas != respuesta_correcta]
  opciones_graficas <- c(respuesta_correcta, sample(distractores, 3))
  opciones_graficas <- sample(opciones_graficas)  # mezclar orden

  # Identificar posición de respuesta correcta
  posicion_correcta <- which(opciones_graficas == respuesta_correcta)

  # Variaciones en la presentación del problema
  formula_presentacion <- sample(c(
    "\\sin(\\alpha) = \\frac{h}{KP}",
    "\\sin(\\alpha) = \\frac{h}{KP}",
    "\\sin(\\alpha) = \\frac{h}{KP}"
  ), 1)

  return(list(
    h = h_valor,
    angulo_inicial = angulo_inicial,
    contexto = contexto,
    color_grafica = color_grafica,
    opciones_graficas = opciones_graficas,
    respuesta_correcta = respuesta_correcta,
    posicion_correcta = posicion_correcta,
    nombre_punto_movil = nombre_punto_movil,
    nombre_segmento_inicio = nombre_segmento_inicio,
    nombre_segmento_fin = nombre_segmento_fin,
    escala_x = escala_x,
    escala_y = escala_y,
    permitir_valores_duplicados = permitir_valores_duplicados,
    formula_presentacion = formula_presentacion
  ))
}

# Prueba de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }

  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))

  cat("Versiones únicas generadas:", n_versiones_unicas, "\n")
})

# Validaciones matemáticas
test_that("Validaciones matemáticas", {
  for(i in 1:50) {
    datos_test <- generar_datos()

    # Validar rangos de valores realistas
    expect_true(datos_test$h >= 1.5 && datos_test$h <= 10,
                info = "El valor de h debe estar en rango realista")

    expect_true(datos_test$angulo_inicial >= 10 && datos_test$angulo_inicial <= 50,
                info = "El ángulo inicial debe estar en rango válido")

    # Verificar coherencia entre parámetros
    expect_true(datos_test$posicion_correcta %in% 1:4,
                info = "La posición correcta debe estar entre 1 y 4")

    expect_true(datos_test$respuesta_correcta == "decreciente",
                info = "La respuesta correcta debe ser 'decreciente'")

    # Verificar que no hay valores inválidos
    expect_true(!is.na(datos_test$h) && !is.null(datos_test$h),
                info = "El valor h no debe ser NA o NULL")
  }
})

# Generar datos para este ejercicio
datos <- generar_datos()

# Función para crear diagrama geométrico TikZ (versión simplificada y estable)
tikz_diagrama_geometrico <- function(h_val, angulo, datos) {
  # Usar configuración simplificada
  punto_movil <- "K"
  punto_inicio <- "Q"
  punto_fin <- "T"

  c("\\begin{tikzpicture}[scale=1.0]",
    "  % Segmento QT horizontal",
    "  \\draw[thick] (0,0) -- (6,0);",
    "  \\node[below] at (0,0) {Q};",
    "  \\node[below] at (6,0) {T};",
    "  ",
    "  % Punto K móvil",
    "  \\node[circle,fill=blue,inner sep=2pt] (K) at (3,0) {};",
    "  \\node[below] at (K) {K};",
    "  ",
    "  % Punto P arriba",
    "  \\node[circle,fill=red,inner sep=2pt] (P) at (3,2.5) {};",
    "  \\node[above] at (P) {P};",
    "  ",
    "  % Línea vertical h",
    "  \\draw[dashed] (K) -- (P);",
    "  \\node[right] at (3.2,1.2) {$h$};",
    "  ",
    "  % Línea para ángulo",
    "  \\draw[thick] (K) -- (5,1.5);",
    "  \\node[right] at (4,0.5) {$\\alpha$};",
    "  ",
    "  % Etiqueta",
    "  \\node[above] at (3,3.5) {Grafica};",
    "\\end{tikzpicture}")
}

# Función para crear gráficas de opciones TikZ (versión simplificada y estable)
tikz_grafica_opcion <- function(tipo, letra, datos) {
  # Usar configuración simplificada para evitar errores
  color <- "blue"  # Color fijo para estabilidad

  base_code <- c(
    "\\begin{tikzpicture}[scale=0.8]",
    "  \\begin{axis}[",
    "    width=4cm, height=3cm,",
    "    xlabel={Angulo},",
    "    ylabel={Distancia},",
    "    xmin=0, xmax=90,",
    "    ymin=0, ymax=10,",
    "    xtick={0,30,60,90},",
    "    ytick={0,2,4,6,8,10},",
    "    grid=major,",
    "    axis lines=left",
    "  ]"
  )

  # Gráficas simplificadas pero representativas
  if(tipo == "constante") {
    # Opción A: Línea horizontal constante
    plot_code <- c(
      "  \\addplot[blue,thick] coordinates {(5,5) (85,5)};"
    )
  } else if(tipo == "decreciente") {
    # Opción B: Curva decreciente (respuesta correcta)
    plot_code <- c(
      "  \\addplot[blue,thick,smooth] coordinates {",
      "    (5,9) (15,7) (25,5.5) (35,4.5) (45,3.5) (55,3) (65,2.5) (75,2.2) (85,2)",
      "  };"
    )
  } else if(tipo == "creciente_decreciente") {
    # Opción C: Curva que sube y luego baja
    plot_code <- c(
      "  \\addplot[blue,thick,smooth] coordinates {",
      "    (5,3) (15,5) (25,7) (35,8) (45,7.5) (55,6) (65,4) (75,2.5) (85,2)",
      "  };"
    )
  } else { # decreciente_creciente
    # Opción D: Línea horizontal constante (similar a A)
    plot_code <- c(
      "  \\addplot[blue,thick] coordinates {(5,4) (85,4)};"
    )
  }

  end_code <- c(
    "  \\end{axis}",
    "\\end{tikzpicture}"
  )

  return(c(base_code, plot_code, end_code))
}

# Librerías TikZ necesarias
tikz_libraries <- c("arrows", "calc", "positioning")
tikz_packages <- c("tikz", "pgfplots")
@

\begin{question}

\Sexpr{datos$contexto}. El ángulo $\alpha$ y la medida $h$ se relacionan mediante la razón trigonométrica $\Sexpr{datos$formula_presentacion}$, de donde se deduce la distancia entre $\Sexpr{datos$nombre_punto_movil}$ y $P$ como $\Sexpr{datos$nombre_punto_movil}P = \frac{h}{\sin(\alpha)}$ o $\Sexpr{datos$nombre_punto_movil}P = h \times \csc(\alpha)$.

<<echo=FALSE, results=tex>>=
include_tikz(tikz_diagrama_geometrico(datos$h, datos$angulo_inicial, datos),
             name = "diagrama", format = typ, width = "8cm",
             library = tikz_libraries, packages = tikz_packages)
@

¿Cuál es la gráfica que muestra las distancias $\Sexpr{datos$nombre_punto_movil}P$, cada vez que $\Sexpr{datos$nombre_punto_movil}$ se mueve sobre el segmento $\Sexpr{datos$nombre_segmento_inicio}\Sexpr{datos$nombre_segmento_fin}$?

\begin{tabular}{cc}
A. & B. \\

<<echo=FALSE, results=tex>>=
include_tikz(tikz_grafica_opcion(datos$opciones_graficas[1], "A", datos),
             name = "grafica_A", format = typ, width = "4cm",
             library = tikz_libraries, packages = tikz_packages)
@

&

<<echo=FALSE, results=tex>>=
include_tikz(tikz_grafica_opcion(datos$opciones_graficas[2], "B", datos),
             name = "grafica_B", format = typ, width = "4cm",
             library = tikz_libraries, packages = tikz_packages)
@

\\
C. & D. \\

<<echo=FALSE, results=tex>>=
include_tikz(tikz_grafica_opcion(datos$opciones_graficas[3], "C", datos),
             name = "grafica_C", format = typ, width = "4cm",
             library = tikz_libraries, packages = tikz_packages)
@

&

<<echo=FALSE, results=tex>>=
include_tikz(tikz_grafica_opcion(datos$opciones_graficas[4], "D", datos),
             name = "grafica_D", format = typ, width = "4cm",
             library = tikz_libraries, packages = tikz_packages)
@

\end{tabular}

<<echo=FALSE, results=tex>>=
# Crear opciones de respuesta
opciones_respuesta <- c("A", "B", "C", "D")
answerlist(opciones_respuesta)
@

\end{question}

\begin{solution}

La relación trigonométrica dada es $\Sexpr{datos$formula_presentacion}$, lo que implica que $\Sexpr{datos$nombre_punto_movil}P = \frac{h}{\sin(\alpha)}$.

\textbf{Análisis matemático del comportamiento de la función:}

\begin{itemize}
\item Cuando $\alpha$ se acerca a $0°$: $\sin(\alpha) \to 0$, por lo tanto $\Sexpr{datos$nombre_punto_movil}P = \frac{h}{\sin(\alpha)} \to +\infty$ (la distancia tiende a infinito).

\item Cuando $\alpha = 30°$: $\sin(30°) = 0.5$, por lo tanto $\Sexpr{datos$nombre_punto_movil}P = \frac{h}{0.5} = 2h$.

\item Cuando $\alpha = 90°$: $\sin(90°) = 1$, por lo tanto $\Sexpr{datos$nombre_punto_movil}P = \frac{h}{1} = h$ (valor mínimo posible).

\item La función $f(\alpha) = \frac{h}{\sin(\alpha)}$ es estrictamente \textbf{decreciente} en el intervalo $(0°, 90°)$ porque:
  \begin{itemize}
  \item $\sin(\alpha)$ es creciente en $(0°, 90°)$
  \item $\frac{1}{\sin(\alpha)}$ es decreciente cuando $\sin(\alpha)$ es creciente
  \item Por lo tanto, $h \cdot \frac{1}{\sin(\alpha)}$ es decreciente
  \end{itemize}
\end{itemize}

\textbf{Análisis de las opciones:}

\begin{itemize}
\item \textbf{Opción A}: Función constante - \textbf{Incorrecta}. La distancia $\Sexpr{datos$nombre_punto_movil}P$ no permanece constante cuando $\alpha$ varía.

\item \textbf{Opción B}: Función decreciente - \textbf{Correcta}. Coincide con el análisis matemático de $\frac{h}{\sin(\alpha)}$.

\item \textbf{Opción C}: Función que crece y luego decrece - \textbf{Incorrecta}. No hay razón matemática para que la función tenga un máximo local.

\item \textbf{Opción D}: Función constante - \textbf{Incorrecta}. Similar al error de la opción A.
\end{itemize}

La respuesta correcta es: \textbf{\Sexpr{opciones_respuesta[datos$posicion_correcta]}}.

<<echo=FALSE, results=tex>>=
# Sistema avanzado de explicaciones para distractores
explicaciones_detalladas <- character(4)

for(i in 1:4) {
  if(i == datos$posicion_correcta) {
    explicaciones_detalladas[i] <- "Verdadero. La función KP = h/sin(α) es estrictamente decreciente en (0°, 90°)."
  } else {
    tipo_grafica <- datos$opciones_graficas[i]
    if(tipo_grafica == "constante") {
      explicaciones_detalladas[i] <- "Falso. Una función constante implicaría que KP no depende de α, lo cual contradice la relación trigonométrica dada."
    } else if(tipo_grafica == "creciente_decreciente") {
      explicaciones_detalladas[i] <- "Falso. La función h/sin(α) no tiene máximos locales en el intervalo (0°, 90°), es monótona decreciente."
    } else {
      explicaciones_detalladas[i] <- "Falso. Esta opción no representa correctamente el comportamiento de la función KP = h/sin(α)."
    }
  }
}

answerlist(explicaciones_detalladas)
@

\end{solution}

% Meta-information ICFES
\exname{Trigonometría - Interpretación de Gráficas}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(c(datos$posicion_correcta == 1, datos$posicion_correcta == 2, datos$posicion_correcta == 3, datos$posicion_correcta == 4))}}
\exshuffle{TRUE}
\exsection{Geometría - Trigonometría}

% Metadatos ICFES adicionales (como comentarios)
% Competencia: interpretacion_representacion
% Nivel de dificultad: 3
% Componente: geometrico_metrico
% Contexto: matematico
% Categoría: geometria
% Tipo: generico

\end{enumerate}
\end{document}
