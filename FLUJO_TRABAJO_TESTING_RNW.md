# 🧪 FLUJO DE TRABAJO PARA TESTING DE ARCHIVOS .RNW

## 📋 Descripción General

Este documento describe el flujo de trabajo completo para realizar testing de archivos `.Rnw` (LaTeX + R) en el proyecto ICFES R-exams. El sistema permite validar la estructura, ejecutar código R y Python, verificar coherencia matemática y probar la compilación a diferentes formatos.

## 🎯 Objetivos del Sistema de Testing

1. **Validar estructura**: Verificar que los archivos `.Rnw` tengan la estructura LaTeX correcta
2. **Ejecutar código R**: Extraer y ejecutar chunks de R para detectar errores
3. **Validar Python**: Probar chunks de Python con matplotlib y numpy
4. **Coherencia matemática**: Verificar que los cálculos y datos sean coherentes
5. **Compilación**: Probar que los archivos se compilen correctamente a HTML, PDF y Moodle

## 📁 Estructura de Archivos

```
Auxiliares/
├── testing_rnw_utils.R              # Utilidades básicas para .Rnw
├── testing_python_chunks.R          # Testing específico de Python
├── testing_coherencia_matematica.R  # Validación matemática
├── testing_compilacion_rnw.R        # Testing de compilación
└── test_rnw_completo.R              # Script principal integrado
```

## 🚀 Uso Rápido

### Opción 1: Script desde línea de comandos
```bash
# Testear un archivo específico
Rscript test_rnw_completo.R archivo.Rnw

# Testear archivo en directorio actual (detección automática)
Rscript test_rnw_completo.R
```

### Opción 2: Desde R interactivo
```r
# Cargar el script principal
source("test_rnw_completo.R")

# Ejecutar suite completa
resultados <- ejecutar_test_suite_completa("archivo.Rnw")

# Ejecutar sin compilación (más rápido)
resultados <- ejecutar_test_suite_completa("archivo.Rnw", incluir_compilacion = FALSE)
```

## 🔧 Configuración Inicial

### Dependencias Requeridas

**Paquetes R:**
```r
install.packages(c("testthat", "exams", "knitr", "reticulate", "stringr", "tools"))
```

**Software del sistema:**
- Python 3.x con matplotlib, numpy
- LaTeX (pdflatex) para compilación PDF
- Pandoc para conversiones

### Verificar Configuración
```r
# Cargar utilidades
source("Auxiliares/testing_rnw_utils.R")

# Verificar que todo esté configurado
configurar_entorno_compilacion()
```

## 📊 Fases del Testing

### Fase 1: Validación de Estructura
- ✅ Verifica `\documentclass`, `\begin{document}`, `\end{document}`
- ✅ Comprueba balance de chunks (`<<>>=` y `@`)
- ✅ Detecta presencia de `\begin{question}` y `\begin{solution}`
- ✅ Calcula puntuación de validez estructural

### Fase 2: Extracción y Ejecución de Código R
- ✅ Extrae todos los chunks de R del archivo
- ✅ Ejecuta el código en entorno controlado
- ✅ Captura errores y warnings
- ✅ Mide tiempo de ejecución

### Fase 3: Validación de Chunks Python
- ✅ Detecta chunks con `engine='python'`
- ✅ Configura matplotlib en modo no interactivo
- ✅ Ejecuta código Python y valida generación de archivos
- ✅ Verifica disponibilidad de librerías (numpy, matplotlib)

### Fase 4: Coherencia Matemática
- ✅ Extrae variables matemáticas (porcentajes, precios, cantidades)
- ✅ Valida rangos numéricos (porcentajes 0-100, precios positivos)
- ✅ Verifica coherencia entre respuestas correctas y distractores
- ✅ Valida cálculos matemáticos específicos

### Fase 5: Compilación (Opcional)
- ✅ Compila a HTML con exams2html()
- ✅ Compila a PDF con exams2pdf() (requiere LaTeX)
- ✅ Compila a Moodle XML con exams2moodle()
- ✅ Valida que los archivos generados sean correctos

## 📈 Interpretación de Resultados

### Estados Generales
- **✅ APROBADO**: ≥80% tests exitosos, sin errores críticos
- **⚠️ APROBADO CON OBSERVACIONES**: 60-79% tests exitosos
- **❌ REPROBADO**: <60% tests exitosos o errores críticos

### Métricas Clave
- **Puntuación de validez estructural**: Debe ser ≥80%
- **Tiempo de ejecución R**: Debe ser <30 segundos
- **Porcentaje éxito Python**: Debe ser ≥80% si hay chunks Python
- **Coherencia matemática**: Debe ser TRUE
- **Porcentaje compilación**: Debe ser ≥80% para formatos testeados

## 🛠️ Funciones Principales

### Utilidades Básicas (`testing_rnw_utils.R`)
```r
# Extraer código R de archivo .Rnw
codigo <- extraer_codigo_r_desde_rnw("archivo.Rnw")

# Ejecutar código extraído
resultado <- ejecutar_codigo_r_extraido(codigo)

# Validar estructura del archivo
validacion <- validar_estructura_rnw("archivo.Rnw")

# Crear suite de tests personalizada
test_suite <- crear_test_suite_rnw("archivo.Rnw")
test_suite()  # Ejecutar
```

### Testing de Python (`testing_python_chunks.R`)
```r
# Configurar Python para testing
configurar_python_testing()

# Extraer solo chunks de Python
chunks_python <- extraer_chunks_python("archivo.Rnw")

# Validar todos los chunks Python
validacion <- validar_chunks_python_rnw("archivo.Rnw")
```

### Coherencia Matemática (`testing_coherencia_matematica.R`)
```r
# Validar coherencia completa (requiere entorno de ejecución)
validacion <- validar_coherencia_completa(entorno)

# Crear tests de coherencia
test_coherencia <- crear_test_coherencia_rnw(entorno)
test_coherencia()
```

### Compilación (`testing_compilacion_rnw.R`)
```r
# Testear compilación completa
resultados <- test_compilacion_completa("archivo.Rnw", c("html", "moodle"))

# Compilar formato específico
resultado_html <- compilar_a_html("archivo.Rnw")
resultado_pdf <- compilar_a_pdf("archivo.Rnw")
resultado_moodle <- compilar_a_moodle("archivo.Rnw")
```

## 🔍 Ejemplo de Uso Completo

```r
# 1. Cargar el sistema
source("test_rnw_completo.R")

# 2. Ejecutar testing completo
archivo <- "06-Estadística-Y-Probabilidad/Pensamiento-Aleatorio/01-Variables-Cualitativas_Distribucion-De-Frecuencias/Gas_natural_porcentaje_maximo_aleatorio_interpretacion_representacion_n2_v1/consumo_gas_natural_porcentaje_maximo_aleatorio_interpretacion_representacion.Rnw"

resultados <- ejecutar_test_suite_completa(archivo)

# 3. Revisar resultados
print(resultados$estado_general)
print(paste("Éxito:", resultados$porcentaje_exito, "%"))

# 4. Ver detalles específicos
if (!resultados$validacion_estructura$chunks_balanceados) {
  print("⚠️ Chunks desbalanceados detectados")
}

if (resultados$ejecucion_r$exito) {
  print("✅ Código R ejecutado correctamente")
} else {
  print(paste("❌ Error R:", resultados$ejecucion_r$error))
}
```

## 📄 Reportes Generados

El sistema genera automáticamente reportes en formato Markdown con:
- Resumen ejecutivo del testing
- Detalles de cada fase
- Métricas y estadísticas
- Recomendaciones para corrección

Archivo: `REPORTE_TESTING_[nombre]_[timestamp].md`

## 🚨 Solución de Problemas Comunes

### Error: "Python no está disponible"
```r
# Configurar Python manualmente
library(reticulate)
use_python("/usr/bin/python3")
```

### Error: "pdflatex no está disponible"
```bash
# Ubuntu/Debian
sudo apt-get install texlive-latex-base texlive-latex-extra

# macOS
brew install --cask mactex
```

### Error: "Chunks desbalanceados"
- Verificar que cada `<<>>=` tenga su correspondiente `@`
- Revisar que no haya chunks anidados

### Error: "Coherencia matemática fallida"
- Verificar cálculos de porcentajes
- Comprobar que las opciones incluyan la respuesta correcta
- Validar rangos de variables numéricas

## 🔄 Integración con Flujo de Desarrollo

### Pre-commit Hook
```bash
#!/bin/bash
# Ejecutar testing antes de commit
for archivo in $(git diff --cached --name-only | grep '\.Rnw$'); do
  Rscript test_rnw_completo.R "$archivo"
  if [ $? -ne 0 ]; then
    echo "❌ Testing falló para $archivo"
    exit 1
  fi
done
```

### CI/CD Pipeline
```yaml
# .github/workflows/test-rnw.yml
name: Test RNW Files
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup R
        uses: r-lib/actions/setup-r@v2
      - name: Install dependencies
        run: Rscript -e "install.packages(c('testthat', 'exams', 'reticulate'))"
      - name: Test RNW files
        run: |
          for file in $(find . -name "*.Rnw"); do
            Rscript test_rnw_completo.R "$file"
          done
```

## 📚 Referencias

- [R-exams Documentation](https://www.r-exams.org/)
- [testthat Package](https://testthat.r-lib.org/)
- [reticulate Package](https://rstudio.github.io/reticulate/)
- [knitr Documentation](https://yihui.org/knitr/)

---

**Última actualización**: 2025-01-11  
**Versión**: 1.0  
**Autor**: Sistema de Testing ICFES
